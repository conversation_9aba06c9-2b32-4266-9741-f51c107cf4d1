﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using LanguageDetection;

namespace CompanyCleanTitleV2
{
    internal class Program
    {
        // Static language detector initialized once for the entire application lifecycle
        private static readonly Lazy<LanguageDetector> _languageDetector = new Lazy<LanguageDetector>(() =>
        {
            var detector = new LanguageDetector();
            detector.AddAllLanguages();
            return detector;
        });

        // Cache for language detection results to avoid repeated detection of the same strings
        private static readonly ConcurrentDictionary<string, string> _languageCache = new ConcurrentDictionary<string, string>();

        // Maximum cache size to prevent memory issues
        private const int MaxCacheSize = 10000;

        static void Main(string[] args)
        {
            // Run performance benchmark first
            RunPerformanceBenchmark();

            Console.WriteLine("\n" + new string('=', 50) + "\n");

            // Test cases for GetDeepCleanCompanyNameV1
            var testCases = new Dictionary<string, string>
            {
                { "Kellogg Brown & Root LLC", "Kellogg Brown Root" },
                { "Siemens AG", "Siemens" },
                { "Telefónica S.A.", "Telefónica" },
                { "株式会社東芝", "東芝" },
                { "भारत लिमिटेड", "भारत" },
                { "Bayerische Motoren Werke AG", "Bayerische Motoren Werke" },
                { "Nestlé S.A.", "Nestlé" },
                { "Gazprom OAO", "Gazprom" },
                { "Companhia Vale do Rio Doce S.A.", "Companhia Vale do Rio Doce" },
                { "Volkswagen Aktiengesellschaft", "Volkswagen" },
                { "Samsung Electronics Co., Ltd.", "Samsung Electronic" },
                { "Tata Consultancy Services Limited", "Tata Consultancy Service" },
                { "中国石油天然气集团公司", "中国石油天然气集团公司" }, // Should remain unchanged if no suffix
                { "شركة أرامكو السعودية", "شركة أرامكو السعودية" }, // Arabic, no suffix
                { "한국전력공사", "한국전력공사" }, // Korean, no suffix
            };

            Console.WriteLine("Testing GetDeepCleanCompanyNameV1:");
            Console.WriteLine("=================================");

            // foreach (var test in testCases)
            // {
            //     var result = GetDeepCleanCompanyName(test.Key);
            //     var passed = result == test.Value;
            //     Console.WriteLine($"Input: {test.Key}");
            //     Console.WriteLine($"Expected: {test.Value}");
            //     Console.WriteLine($"Got: '{result}'");
            //     Console.WriteLine($"Test {(passed ? "PASSED" : "FAILED")}");
            //     Console.WriteLine("-------------------");
            // }

            Console.WriteLine("\n" + new string('=', 50) + "\n");

            // Test cases for GetCleanJobPostingTitle - English and Multi-Language
            var jobTitleTestCases = new Dictionary<string, string>
            {
                // === ENGLISH TEST CASES ===
                { "Counselor I - (LAC)", "Counselor I" },
                { "Counselor II - (LAC)", "Counselor II" },
                { "Counselor III - (LAC)", "Counselor III" },
                { "Clinician I - (YOP-VSP)", "Clinician I" },
                { "Recreational Therapist - (PGYCC)", "Recreational Therapist" },
                { "Clinician (LCSW) - Los Angeles County Training Center", "Clinician (LCSW)" },
                { "Intensive Case Manager- DHS/JIR", "Intensive Case Manager" },
                { "Catering - Food Service Cook", "Food Service Cook" },
                { "Respiratory Care Therapist II, Transport Team", "Respiratory Care Therapist II" },
                { "Registered Nurse - Operating Room 0630 1.0 FTE", "Registered Nurse, Operating Room" },
                { "Registered Nurse - Critical Care Float (Day/Night)", "Registered Nurse, Critical Care Float" },
                { "Registered Nurse - Cardiac Specialty Unit (Per Diem, Days)", "Registered Nurse, Cardiac Specialty Unit" },
                { "Floors", "Flooring Specialist" },
                { "Sales Associate-5195", "Sales Associate" },
                { "Certified Nurse Assistant Trainee", "Certified Nurse Assistant Trainee" },
                { "Security Officer, Evening Shift, Security Services", "Security Officer" },
                { "Vascular Access Registered Nurse, PT Day Shift, Heart and Vascular Unit", "Vascular Access Registered Nurse" },
                { "Certified Nursing Assistant (CNA), Per Diem Night Shift, Float Pool", "Certified Nursing Assistant (CNA)" },
                { "Registered Nurse (RN), Variable Part Time Day Shift, Operating Room (OR)", "Registered Nurse, Operating Room (OR)" },
                { "Radiology Aide, Evening Shift, Radiology", "Radiology Aide" },
                { "Patient Safety and Risk Management Advisor, Day Shift, Quality & Patient Safety", "Patient Safety and Risk Management Advisor" },
                { "Registered Nurse (RN), Day Shift, Med Surg", "Registered Nurse (RN)" },
                { "Patient Care Technician (CNA), Day Shift, Rehabilitation", "Patient Care Technician (CNA)" },
                { "Pharmacy Technician (Tech II), Rotating Shifts, Pharmacy", "Pharmacy Technician" },
                { "Behavioral Health Therapist, Day Shift, Behavioral Health (Inpatient)", "Behavioral Health Therapist" },
                { "VP, Head of Architecture", "Vice President, Head of Architecture" },
                { "Trust Officer", "Trust Officer" },
                { "Case Designer, Highland Capital Brokerage", "Case Designer" },
                { "Delivery Driver(06032) - 45 Beach Gate Shopping Center", "Delivery Driver" },

                // === GERMAN TEST CASES ===
                { "Sicherheitsbeamter - (BER)", "Sicherheitsbeamter" },
                { "Krankenschwester, Abendschicht, Sicherheitsdienst", "Krankenschwester" },
                { "Apotheker (PharmD), Tagschicht, Apotheke", "Apotheker (PharmD)" },
                { "Therapeut II, Wechselschichten, Rehabilitation", "Therapeut II" },
                { "Berater - (MUC-HAM)", "Berater" },
                { "Verkaufsassistent-4567", "Verkaufsassistent" },

                // === FRENCH TEST CASES ===
                { "Infirmière - (PAR)", "Infirmière" },
                { "Agent de Sécurité, Équipe du Soir, Services de Sécurité", "Agent de Sécurité" },
                { "Pharmacien (PharmD), Équipe de Jour, Pharmacie", "Pharmacien (PharmD)" },
                { "Thérapeute III, Équipes Tournantes, Réhabilitation", "Thérapeute III" },
                { "Conseiller - (LYO-MAR)", "Conseiller" },
                { "Assistant Commercial-7890", "Assistant Commercial" },

                // === SPANISH TEST CASES ===
                { "Enfermero - (MAD)", "Enfermero" },
                { "Oficial de Seguridad, Turno de Tarde, Servicios de Seguridad", "Oficial de Seguridad" },
                { "Farmacéutico (PharmD), Turno de Día, Farmacia", "Farmacéutico (PharmD)" },
                { "Terapeuta I, Turnos Rotativos, Rehabilitación", "Terapeuta I" },
                { "Consejero - (BCN-SEV)", "Consejero" },
                { "Asociado de Ventas-3456", "Asociado de Ventas" },

                // === ITALIAN TEST CASES ===
                { "Infermiere - (ROM)", "Infermiere" },
                { "Ufficiale di Sicurezza, Turno Serale, Servizi di Sicurezza", "Ufficiale di Sicurezza" },
                { "Farmacista (PharmD), Turno Diurno, Farmacia", "Farmacista (PharmD)" },
                { "Terapista II, Turni Rotanti, Riabilitazione", "Terapista II" },
                { "Consulente - (MIL-NAP)", "Consulente" },
                { "Assistente Vendite-2345", "Assistente Vendite" },

                // === CHINESE TEST CASES ===
                { "护士 - (BEJ)", "护士" },
                { "安保员, 晚班, 安保服务", "安保员" },
                { "药剂师 (PharmD), 日班, 药房", "药剂师 (PharmD)" },
                { "治疗师 III, 轮班, 康复科", "治疗师 III" },
                { "顾问 - (SHA-GUA)", "顾问" },
                { "销售助理-6789", "销售助理" },

                // === JAPANESE TEST CASES ===
                { "看護師 - (TOK)", "看護師" },
                { "警備員, 夕方シフト, セキュリティサービス", "警備員" },
                { "薬剤師 (PharmD), 日勤シフト, 薬局", "薬剤師 (PharmD)" },
                { "セラピスト I, ローテーションシフト, リハビリテーション", "セラピスト I" },
                { "カウンセラー - (OSA-KYO)", "カウンセラー" },
                { "営業アシスタント-1234", "営業アシスタント" },

                // === HINDI TEST CASES ===
                { "नर्स - (DEL)", "नर्स" },
                { "सुरक्षा अधिकारी, शाम की पाली, सुरक्षा सेवाएं", "सुरक्षा अधिकारी" },
                { "फार्मासिस्ट (PharmD), दिन की पाली, फार्मेसी", "फार्मासिस्ट (PharmD)" },
                { "चिकित्सक II, घूर्णी पालियां, पुनर्वास", "चिकित्सक II" },
                { "सलाहकार - (MUM-BAN)", "सलाहकार" },
                { "बिक्री सहायक-5678", "बिक्री सहायक" },

                // === POLISH TEST CASES ===
                { "Pielęgniarka - (WAR)", "Pielęgniarka" },
                { "Oficer Bezpieczeństwa, Zmiana Wieczorna, Służby Bezpieczeństwa", "Oficer Bezpieczeństwa" },
                { "Farmaceuta (PharmD), Zmiana Dzienna, Apteka", "Farmaceuta (PharmD)" },
                { "Terapeuta III, Zmiany Rotacyjne, Rehabilitacja", "Terapeuta III" },
                { "Doradca - (KRA-GDA)", "Doradca" },
                { "Asystent Sprzedaży-9876", "Asystent Sprzedaży" }
            };

            Console.WriteLine("Testing GetCleanJobPostingTitle:");
            Console.WriteLine("================================");

            // Group test cases by language for better organization
            var languageGroups = new Dictionary<string, List<KeyValuePair<string, string>>>
            {
                { "English", new List<KeyValuePair<string, string>>() },
                { "German", new List<KeyValuePair<string, string>>() },
                { "French", new List<KeyValuePair<string, string>>() },
                { "Spanish", new List<KeyValuePair<string, string>>() },
                { "Italian", new List<KeyValuePair<string, string>>() },
                { "Chinese", new List<KeyValuePair<string, string>>() },
                { "Japanese", new List<KeyValuePair<string, string>>() },
                { "Hindi", new List<KeyValuePair<string, string>>() },
                { "Polish", new List<KeyValuePair<string, string>>() }
            };

            // Categorize test cases by language
            foreach (var test in jobTitleTestCases)
            {
                var detectedLang = DetectLanguageOptimized(test.Key);
                switch (detectedLang)
                {
                    case "en": languageGroups["English"].Add(test); break;
                    case "de": languageGroups["German"].Add(test); break;
                    case "fr": languageGroups["French"].Add(test); break;
                    case "es": languageGroups["Spanish"].Add(test); break;
                    case "it": languageGroups["Italian"].Add(test); break;
                    case "zh": languageGroups["Chinese"].Add(test); break;
                    case "ja": languageGroups["Japanese"].Add(test); break;
                    case "hi": languageGroups["Hindi"].Add(test); break;
                    case "pl": languageGroups["Polish"].Add(test); break;
                    default: languageGroups["English"].Add(test); break; // Default to English
                }
            }

            int totalTests = 0;
            int totalPassed = 0;

            // Test each language group
            foreach (var group in languageGroups)
            {
                if (group.Value.Count == 0) continue;

                Console.WriteLine($"\n🌍 {group.Key.ToUpper()} TEST CASES");
                Console.WriteLine(new string('=', 50));

                int languagePassed = 0;
                int languageTotal = group.Value.Count;

                foreach (var test in group.Value)
                {
                    var result = GetCleanJobPostingTitle(test.Key);
                    var passed = result == test.Value;

                    Console.WriteLine($"📝 Input:    {test.Key}");
                    Console.WriteLine($"✅ Expected: {test.Value}");
                    Console.WriteLine($"🔍 Got:      '{result}'");
                    Console.WriteLine($"📊 Result:   {(passed ? "✅ PASSED" : "❌ FAILED")}");
                    Console.WriteLine(new string('-', 40));

                    if (passed) languagePassed++;
                    totalTests++;
                    if (passed) totalPassed++;
                }

                Console.WriteLine($"📈 {group.Key} Summary: {languagePassed}/{languageTotal} PASSED ({(double)languagePassed/languageTotal*100:F1}%)");
            }

            Console.WriteLine($"\n🎯 OVERALL SUMMARY");
            Console.WriteLine(new string('=', 50));
            Console.WriteLine($"📊 Total Tests: {totalTests}");
            Console.WriteLine($"✅ Passed: {totalPassed}");
            Console.WriteLine($"❌ Failed: {totalTests - totalPassed}");
            Console.WriteLine($"📈 Success Rate: {(double)totalPassed/totalTests*100:F1}%");
            Console.WriteLine(new string('=', 50));
        }

        /// <summary>
        /// Benchmarks the performance improvement of the optimized language detection
        /// </summary>
        private static void RunPerformanceBenchmark()
        {
            Console.WriteLine("Performance Benchmark: Language Detection Optimization");
            Console.WriteLine("====================================================");

            var testStrings = new[]
            {
                "Kellogg Brown & Root LLC",
                "Siemens AG",
                "Telefónica S.A.",
                "株式会社東芝",
                "भारत लिमिटेड",
                "Bayerische Motoren Werke AG",
                "Nestlé S.A.",
                "Samsung Electronics Co., Ltd.",
                "Tata Consultancy Services Limited"
            };

            const int iterations = 100;

            // Benchmark optimized version (current implementation)
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            for (int i = 0; i < iterations; i++)
            {
                foreach (var testString in testStrings)
                {
                    DetectLanguageOptimized($"{testString} {i}");
                }
            }

            stopwatch.Stop();
            var optimizedTime = stopwatch.ElapsedMilliseconds;

            Console.WriteLine($"Optimized Implementation:");
            Console.WriteLine($"  - {iterations} iterations × {testStrings.Length} strings = {iterations * testStrings.Length} total detections");
            Console.WriteLine($"  - Total time: {optimizedTime} ms");
            Console.WriteLine($"  - Average per detection: {(double)optimizedTime / (iterations * testStrings.Length):F2} ms");
            Console.WriteLine($"  - Cache hits after first iteration: {_languageCache.Count} cached entries");

            // Test cache effectiveness by running again
            stopwatch.Restart();
            for (int i = 0; i < iterations; i++)
            {
                foreach (var testString in testStrings)
                {
                    DetectLanguageOptimized(testString);
                }
            }
            stopwatch.Stop();
            var cachedTime = stopwatch.ElapsedMilliseconds;

            Console.WriteLine($"\nWith Cache (second run):");
            Console.WriteLine($"  - Total time: {cachedTime} ms");
            Console.WriteLine($"  - Average per detection: {(double)cachedTime / (iterations * testStrings.Length):F2} ms");
            Console.WriteLine($"  - Performance improvement: {((double)(optimizedTime - cachedTime) / optimizedTime * 100):F1}% faster");
        }

        // Dictionary to store language-specific patterns
        private static readonly Dictionary<string, LanguagePatterns> LanguageWordSets = InitializeLanguageWordSets();

        // Dictionary to store job title specific language patterns
        private static readonly Dictionary<string, JobTitleLanguagePatterns> JobTitleLanguageWordSets = InitializeJobTitleLanguageWordSets();

        // Class to hold patterns for each language
        private class LanguagePatterns
        {
            public string SuffixPattern { get; set; }
            public string ConjunctionPattern { get; set; }
            public string PossessivePattern { get; set; }
        }

        // Class to hold job title specific patterns for each language
        private class JobTitleLanguagePatterns
        {
            public string ShiftPattern { get; set; }
            public string DepartmentPattern { get; set; }
            public string EmploymentTypePattern { get; set; }
            public string LocationCodePattern { get; set; }
            public string IdentifierPattern { get; set; }
            public string LocationDetailPattern { get; set; }
        }

        // Initialize language-specific word sets
        private static Dictionary<string, LanguagePatterns> InitializeLanguageWordSets()
        {
            var wordSets = new Dictionary<string, LanguagePatterns>
        {
            // English
            {
                "en", new LanguagePatterns
                {
                    SuffixPattern = @"\b(incorporated|corporation|Corporate|Cooperatives|Cooperative|coop|inc|llc|corp|corps|company|limited|ltd|co|LLP|LP|L\.L\.C|Co\.,? Ltd\.|Co\.|Ltd\.|S\.A\.|S\.A\.?|OAO|Aktiengesellschaft)\b",
                    ConjunctionPattern = @"\b(and|of|in|by|for|or)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // German
            {
                "de", new LanguagePatterns
                {
                    SuffixPattern = @"\b(GmbH|AG|KG|OHG|EG|SE)\b",
                    ConjunctionPattern = @"\b(und|von|in|bei|für|oder)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // French
            {
                "fr", new LanguagePatterns
                {
                    SuffixPattern = @"\b(SA|S\.A\.|SARL|SAS|SE)\b",
                    ConjunctionPattern = @"\b(et|de|à|pour|ou)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Spanish
            {
                "es", new LanguagePatterns
                {
                    SuffixPattern = @"\b(SA|S\.A\.?|SL|SRL)\b",
                    ConjunctionPattern = @"\b(y|de|en|por|para|o)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Italian
            {
                "it", new LanguagePatterns
                {
                    SuffixPattern = @"\b(SpA|SRL|SRLS)\b",
                    ConjunctionPattern = @"\b(e|di|in|per|o)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Portuguese
            {
                "pt", new LanguagePatterns
                {
                    SuffixPattern = @"\b(SA|S\.A\.|Lda)\b",
                    ConjunctionPattern = @"\b(e|de|em|por|para|ou)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Dutch
            {
                "nl", new LanguagePatterns
                {
                    SuffixPattern = @"\b(NV|BV|Vof)\b",
                    ConjunctionPattern = @"\b(en|van|in|voor|of)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Polish
            {
                "pl", new LanguagePatterns
                {
                    SuffixPattern = @"\b(Sp\.z\.o\.o\.|SA|s\.c\.)\b",
                    ConjunctionPattern = @"\b(i|w|z|lub)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Swedish
            {
                "sv", new LanguagePatterns
                {
                    SuffixPattern = @"\b(AB)\b",
                    ConjunctionPattern = @"\b(och|i|för|eller)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Danish
            {
                "da", new LanguagePatterns
                {
                    SuffixPattern = @"\b(A\/S|ApS)\b",
                    ConjunctionPattern = @"\b(og|i|for|eller)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Finnish
            {
                "fi", new LanguagePatterns
                {
                    SuffixPattern = @"\b(Oy|OYJ)\b",
                    ConjunctionPattern = @"\b(ja|in|tai)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Czech
            {
                "cs", new LanguagePatterns
                {
                    SuffixPattern = @"\b(s\.r\.o\.|a\.s\.)\b",
                    ConjunctionPattern = @"\b(a|v|z|nebo)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Slovak
            {
                "sk", new LanguagePatterns
                {
                    SuffixPattern = @"\b(s\.r\.o\.)\b",
                    ConjunctionPattern = @"\b(a|v|z|alebo)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Greek
            {
                "el", new LanguagePatterns
                {
                    SuffixPattern = @"\b(ΑΕ|ΕΠΕ)\b",
                    ConjunctionPattern = @"\b(και|σε|για|ή)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Bulgarian
            {
                "bg", new LanguagePatterns
                {
                    SuffixPattern = @"\b(ЕООД|АД)\b",
                    ConjunctionPattern = @"\b(и|в|за|или)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Hungarian
            {
                "hu", new LanguagePatterns
                {
                    SuffixPattern = @"\b(Kft|Zrt|Nyrt)\b",
                    ConjunctionPattern = @"\b(és|ban|ben|val|vel|vagy)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Croatian
            {
                "hr", new LanguagePatterns
                {
                    SuffixPattern = @"\b(d\.o\.o\.|d\.d\.)\b",
                    ConjunctionPattern = @"\b(i|u|za|ili)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Estonian
            {
                "et", new LanguagePatterns
                {
                    SuffixPattern = @"\b(OÜ|AS)\b",
                    ConjunctionPattern = @"\b(ja|või)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Latvian
            {
                "lv", new LanguagePatterns
                {
                    SuffixPattern = @"\b(SIA|AS)\b",
                    ConjunctionPattern = @"\b(un|vai)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Lithuanian
            {
                "lt", new LanguagePatterns
                {
                    SuffixPattern = @"\b(UAB)\b",
                    ConjunctionPattern = @"\b(ir|ar)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Romanian
            {
                "ro", new LanguagePatterns
                {
                    SuffixPattern = @"\b(SRL|SA)\b",
                    ConjunctionPattern = @"\b(şi|în|pentru|sau)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Slovene
            {
                "sl", new LanguagePatterns
                {
                    SuffixPattern = @"\b(d\.o\.o\.|d\.d\.)\b",
                    ConjunctionPattern = @"\b(in|za|ali)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Maltese
            {
                "mt", new LanguagePatterns
                {
                    SuffixPattern = @"\b(Ltd)\b",
                    ConjunctionPattern = @"\b(u|ta’|għal|jew)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Hindi
            {
                "hi", new LanguagePatterns
                {
                    SuffixPattern = @"\b(लिमिटेड|प्राइवेट|पब्लिक)\b",
                    ConjunctionPattern = @"\b(और|में|के|या)\b",
                    PossessivePattern = @"['’]s\b",
                }
            },
            // Malay
            {
                "ms", new LanguagePatterns
                {
                    SuffixPattern = @"\b(Sdn Bhd|Bhd)\b",
                    ConjunctionPattern = @"\b(dan|di|untuk|atau)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Mandarin
            {
                "zh", new LanguagePatterns
                {
                    SuffixPattern = @"\b(有限公司|股份公司)\b",
                    ConjunctionPattern = @"\b(和|在|为|或)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Tamil
            {
                "ta", new LanguagePatterns
                {
                    SuffixPattern = @"\b(லிமிடெட்|பிரைவேட்)\b",
                    ConjunctionPattern = @"\b(மற்றும்|இல்|அல்லது)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Japanese
            {
                "ja", new LanguagePatterns
                {
                    // Match 株式会社 or 合同会社 at the start, or surrounded by spaces (handles both cases)
                    SuffixPattern = @"^(株式会社|合同会社)|\s(株式会社|合同会社)\s?",
                    ConjunctionPattern = @"(?:^|\s)(と|で|に|や|または)(?:\s|$)",
                    PossessivePattern = @"['’]s\b",
                }
            },

            // Add more languages as needed (e.g., Māori, Irish, Luxembourgish, etc.)
        };
            return wordSets;
        }

        // Initialize job title specific language-specific word sets
        private static Dictionary<string, JobTitleLanguagePatterns> InitializeJobTitleLanguageWordSets()
        {
            var jobTitleWordSets = new Dictionary<string, JobTitleLanguagePatterns>
            {
                // English
                {
                    "en", new JobTitleLanguagePatterns
                    {
                        ShiftPattern = @",\s*(Evening Shift|Day Shift|Night Shift|PT Day Shift|Variable Part Time Day Shift|Per Diem Night Shift|Per Diem Nights|Rotating Shifts)\b",
                        DepartmentPattern = @",\s*(Security Services|Heart and Vascular Unit|Float Pool|Med Surg General Surgery|Med Surg|Quality & Patient Safety|Behavioral Health \(Inpatient\)|Behavioral Health|Labor & Delivery|Critical Care Float|Cardiac Specialty Unit|Rehabilitation|Pharmacy|Transport Team|Radiology)\b",
                        EmploymentTypePattern = @",\s*(Variable Part Time|PT|\d+\.\d+\s+FTE)\b",
                        LocationCodePattern = @"\s*-\s*\([A-Z]{2,5}(-[A-Z]{2,5})*\)",
                        IdentifierPattern = @"-?\d{4,5}\b|\b\d{4}\s+\d+\.\d+\s+FTE\b|\(\d{4,5}\)|\s+\d+\.\d+\s+FTE\b",
                        LocationDetailPattern = @"\s*-\s*[^,]+\s+(Shopping Center|Training Center|Brokerage)|,\s*[^,]+\s+(Shopping Center|Training Center|Brokerage)"
                    }
                },
                // German
                {
                    "de", new JobTitleLanguagePatterns
                    {
                        ShiftPattern = @",\s*(Abendschicht|Tagschicht|Nachtschicht|Teilzeit Tagschicht|Variable Teilzeit Tagschicht|Aushilfe Nachtschicht|Aushilfe Nächte|Wechselschichten)",
                        DepartmentPattern = @",\s*(Sicherheitsdienst|Herz- und Gefäßeinheit|Springerpool|Medizinisch-Chirurgische Allgemeinchirurgie|Medizinisch-Chirurgisch|Qualität & Patientensicherheit|Verhaltensgesundheit \(Stationär\)|Verhaltensgesundheit|Entbindung|Intensivpflege Springer|Herzspezialeinheit|Rehabilitation|Apotheke|Transportteam|Radiologie)",
                        EmploymentTypePattern = @",\s*(Variable Teilzeit|Teilzeit|\d+\.\d+\s+VZÄ)\b",
                        LocationCodePattern = @"\s*-\s*\([A-Z]{2,5}(-[A-Z]{2,5})*\)",
                        IdentifierPattern = @"-?\d{4,5}\b|\b\d{4}\s+\d+\.\d+\s+VZÄ\b|\(\d{4,5}\)|\s+\d+\.\d+\s+VZÄ\b",
                        LocationDetailPattern = @"\s*-\s*[^,]+\s+(Einkaufszentrum|Ausbildungszentrum|Maklerhaus)|,\s*[^,]+\s+(Einkaufszentrum|Ausbildungszentrum|Maklerhaus)"
                    }
                },
                // French
                {
                    "fr", new JobTitleLanguagePatterns
                    {
                        ShiftPattern = @",\s*(Équipe du Soir|Équipe de Jour|Équipe de Nuit|Temps Partiel Jour|Temps Partiel Variable Jour|Intérim Nuit|Intérim Nuits|Équipes Tournantes)\b",
                        DepartmentPattern = @",\s*(Services de Sécurité|Unité Cardiaque et Vasculaire|Pool Flottant|Chirurgie Générale Médicale|Médico-Chirurgical|Qualité & Sécurité des Patients|Santé Comportementale \(Hospitalisation\)|Santé Comportementale|Accouchement|Soins Intensifs Flottant|Unité Spécialisée Cardiaque|Réhabilitation|Pharmacie|Équipe de Transport|Radiologie)\b",
                        EmploymentTypePattern = @",\s*(Temps Partiel Variable|Temps Partiel|\d+\.\d+\s+ETP)\b",
                        LocationCodePattern = @"\s*-\s*\([A-Z]{2,5}(-[A-Z]{2,5})*\)",
                        IdentifierPattern = @"-?\d{4,5}\b|\b\d{4}\s+\d+\.\d+\s+ETP\b|\(\d{4,5}\)|\s+\d+\.\d+\s+ETP\b",
                        LocationDetailPattern = @"\s*-\s*[^,]+\s+(Centre Commercial|Centre de Formation|Courtage)|,\s*[^,]+\s+(Centre Commercial|Centre de Formation|Courtage)"
                    }
                },
                // Spanish
                {
                    "es", new JobTitleLanguagePatterns
                    {
                        ShiftPattern = @",\s*(Turno de Tarde|Turno de Día|Turno de Noche|Tiempo Parcial Día|Tiempo Parcial Variable Día|Por Horas Noche|Por Horas Noches|Turnos Rotativos)\b",
                        DepartmentPattern = @",\s*(Servicios de Seguridad|Unidad Cardíaca y Vascular|Grupo Flotante|Cirugía General Médica|Médico-Quirúrgico|Calidad y Seguridad del Paciente|Salud Conductual \(Hospitalización\)|Salud Conductual|Parto|Cuidados Intensivos Flotante|Unidad Especializada Cardíaca|Rehabilitación|Farmacia|Equipo de Transporte|Radiología)\b",
                        EmploymentTypePattern = @",\s*(Tiempo Parcial Variable|Tiempo Parcial|\d+\.\d+\s+ETC)\b",
                        LocationCodePattern = @"\s*-\s*\([A-Z]{2,5}(-[A-Z]{2,5})*\)",
                        IdentifierPattern = @"-?\d{4,5}\b|\b\d{4}\s+\d+\.\d+\s+ETC\b|\(\d{4,5}\)|\s+\d+\.\d+\s+ETC\b",
                        LocationDetailPattern = @"\s*-\s*[^,]+\s+(Centro Comercial|Centro de Entrenamiento|Corretaje)|,\s*[^,]+\s+(Centro Comercial|Centro de Entrenamiento|Corretaje)"
                    }
                },
                // Italian
                {
                    "it", new JobTitleLanguagePatterns
                    {
                        ShiftPattern = @",\s*(Turno Serale|Turno Diurno|Turno Notturno|Part Time Diurno|Part Time Variabile Diurno|A Chiamata Notte|A Chiamata Notti|Turni Rotanti)",
                        DepartmentPattern = @",\s*(Servizi di Sicurezza|Unità Cardiaca e Vascolare|Pool Fluttuante|Chirurgia Generale Medica|Medico-Chirurgico|Qualità e Sicurezza del Paziente|Salute Comportamentale \(Ricovero\)|Salute Comportamentale|Parto|Terapia Intensiva Fluttuante|Unità Specializzata Cardiaca|Riabilitazione|Farmacia|Team di Trasporto|Radiologia)",
                        EmploymentTypePattern = @",\s*(Part Time Variabile|Part Time|\d+\.\d+\s+ETP)\b",
                        LocationCodePattern = @"\s*-\s*\([A-Z]{2,5}(-[A-Z]{2,5})*\)",
                        IdentifierPattern = @"-?\d{4,5}\b|\b\d{4}\s+\d+\.\d+\s+ETP\b|\(\d{4,5}\)|\s+\d+\.\d+\s+ETP\b",
                        LocationDetailPattern = @"\s*-\s*[^,]+\s+(Centro Commerciale|Centro di Formazione|Intermediazione)|,\s*[^,]+\s+(Centro Commerciale|Centro di Formazione|Intermediazione)"
                    }
                },
                // Portuguese
                {
                    "pt", new JobTitleLanguagePatterns
                    {
                        ShiftPattern = @",\s*(Turno da Tarde|Turno do Dia|Turno da Noite|Meio Período Dia|Meio Período Variável Dia|Por Demanda Noite|Por Demanda Noites|Turnos Rotativos)\b",
                        DepartmentPattern = @",\s*(Serviços de Segurança|Unidade Cardíaca e Vascular|Pool Flutuante|Cirurgia Geral Médica|Médico-Cirúrgico|Qualidade e Segurança do Paciente|Saúde Comportamental \(Internação\)|Saúde Comportamental|Parto|Cuidados Intensivos Flutuante|Unidade Especializada Cardíaca|Reabilitação|Farmácia|Equipe de Transporte|Radiologia)\b",
                        EmploymentTypePattern = @",\s*(Meio Período Variável|Meio Período|\d+\.\d+\s+ETC)\b",
                        LocationCodePattern = @"\s*-\s*\([A-Z]{2,5}(-[A-Z]{2,5})*\)",
                        IdentifierPattern = @"-?\d{4,5}\b|\b\d{4}\s+\d+\.\d+\s+ETC\b|\(\d{4,5}\)|\s+\d+\.\d+\s+ETC\b",
                        LocationDetailPattern = @"\s*-\s*[^,]+\s+(Centro Comercial|Centro de Treinamento|Corretagem)|,\s*[^,]+\s+(Centro Comercial|Centro de Treinamento|Corretagem)"
                    }
                },
                // Dutch
                {
                    "nl", new JobTitleLanguagePatterns
                    {
                        ShiftPattern = @",\s*(Avonddienst|Dagdienst|Nachtdienst|Deeltijd Dag|Variabele Deeltijd Dag|Oproep Nacht|Oproep Nachten|Wisselende Diensten)\b",
                        DepartmentPattern = @",\s*(Beveiligingsdiensten|Hart- en Vaateenheid|Flexibele Pool|Medisch-Chirurgische Algemene Chirurgie|Medisch-Chirurgisch|Kwaliteit & Patiëntveiligheid|Gedragsgezondheidszorg \(Klinisch\)|Gedragsgezondheidszorg|Bevalling|Intensieve Zorg Flexibel|Gespecialiseerde Harteenheid|Revalidatie|Apotheek|Transportteam|Radiologie)\b",
                        EmploymentTypePattern = @",\s*(Variabele Deeltijd|Deeltijd|\d+\.\d+\s+FTE)\b",
                        LocationCodePattern = @"\s*-\s*\([A-Z]{2,5}(-[A-Z]{2,5})*\)",
                        IdentifierPattern = @"-?\d{4,5}\b|\b\d{4}\s+\d+\.\d+\s+FTE\b|\(\d{4,5}\)|\s+\d+\.\d+\s+FTE\b",
                        LocationDetailPattern = @"\s*-\s*[^,]+\s+(Winkelcentrum|Trainingscentrum|Makelaardij)|,\s*[^,]+\s+(Winkelcentrum|Trainingscentrum|Makelaardij)"
                    }
                },
                // Polish
                {
                    "pl", new JobTitleLanguagePatterns
                    {
                        ShiftPattern = @",\s*(Zmiana Wieczorna|Zmiana Dzienna|Zmiana Nocna|Niepełny Etat Dzienny|Zmienny Niepełny Etat Dzienny|Na Wezwanie Noc|Na Wezwanie Noce|Zmiany Rotacyjne)",
                        DepartmentPattern = @",\s*(Służby Bezpieczeństwa|Oddział Kardiologiczno-Naczyniowy|Pula Zastępcza|Chirurgia Ogólna Medyczna|Medyczno-Chirurgiczny|Jakość i Bezpieczeństwo Pacjenta|Zdrowie Behawioralne \(Stacjonarne\)|Zdrowie Behawioralne|Poród|Intensywna Opieka Zastępcza|Wyspecjalizowany Oddział Kardiologiczny|Rehabilitacja|Apteka|Zespół Transportowy|Radiologia)",
                        EmploymentTypePattern = @",\s*(Zmienny Niepełny Etat|Niepełny Etat|\d+\.\d+\s+EPC)",
                        LocationCodePattern = @"\s*-\s*\([A-Z]{2,5}(-[A-Z]{2,5})*\)",
                        IdentifierPattern = @"-\d{4,5}|\(\d{4,5}\)",
                        LocationDetailPattern = @"\s*-\s*[^,]+\s+(Centrum Handlowe|Centrum Szkoleniowe|Pośrednictwo)|,\s*[^,]+\s+(Centrum Handlowe|Centrum Szkoleniowe|Pośrednictwo)"
                    }
                },
                // Swedish
                {
                    "sv", new JobTitleLanguagePatterns
                    {
                        ShiftPattern = @",\s*(Kvällsskift|Dagskift|Nattskift|Deltid Dag|Variabel Deltid Dag|Timanställning Natt|Timanställning Nätter|Roterande Skift)\b",
                        DepartmentPattern = @",\s*(Säkerhetstjänster|Hjärt- och Kärlenheten|Flexibel Pool|Medicinsk-Kirurgisk Allmän Kirurgi|Medicinsk-Kirurgisk|Kvalitet & Patientsäkerhet|Beteendehälsa \(Slutenvård\)|Beteendehälsa|Förlossning|Intensivvård Flexibel|Specialiserad Hjärtenhet|Rehabilitering|Apotek|Transportteam|Radiologi)\b",
                        EmploymentTypePattern = @",\s*(Variabel Deltid|Deltid|\d+\.\d+\s+HTA)\b",
                        LocationCodePattern = @"\s*-\s*\([A-Z]{2,5}(-[A-Z]{2,5})*\)",
                        IdentifierPattern = @"-?\d{4,5}\b|\b\d{4}\s+\d+\.\d+\s+HTA\b|\(\d{4,5}\)|\s+\d+\.\d+\s+HTA\b",
                        LocationDetailPattern = @"\s*-\s*[^,]+\s+(Köpcentrum|Utbildningscentrum|Mäkleri)|,\s*[^,]+\s+(Köpcentrum|Utbildningscentrum|Mäkleri)"
                    }
                },
                // Danish
                {
                    "da", new JobTitleLanguagePatterns
                    {
                        ShiftPattern = @",\s*(Aftenvagt|Dagvagt|Nattevagt|Deltid Dag|Variabel Deltid Dag|Vikariat Nat|Vikariat Nætter|Skiftende Vagter)\b",
                        DepartmentPattern = @",\s*(Sikkerhedstjenester|Hjerte- og Karsygdomsafdeling|Fleksibel Pulje|Medicinsk-Kirurgisk Generel Kirurgi|Medicinsk-Kirurgisk|Kvalitet & Patientsikkerhed|Adfærdssundhed \(Indlæggelse\)|Adfærdssundhed|Fødsel|Intensiv Pleje Fleksibel|Specialiseret Hjerteafdeling|Rehabilitering|Apotek|Transportteam|Radiologi)\b",
                        EmploymentTypePattern = @",\s*(Variabel Deltid|Deltid|\d+\.\d+\s+FTE)\b",
                        LocationCodePattern = @"\s*-\s*\([A-Z]{2,5}(-[A-Z]{2,5})*\)",
                        IdentifierPattern = @"-?\d{4,5}\b|\b\d{4}\s+\d+\.\d+\s+FTE\b|\(\d{4,5}\)|\s+\d+\.\d+\s+FTE\b",
                        LocationDetailPattern = @"\s*-\s*[^,]+\s+(Indkøbscenter|Træningscenter|Mæglervirksomhed)|,\s*[^,]+\s+(Indkøbscenter|Træningscenter|Mæglervirksomhed)"
                    }
                },
                // Finnish
                {
                    "fi", new JobTitleLanguagePatterns
                    {
                        ShiftPattern = @",\s*(Iltavuoro|Päivävuoro|Yövuoro|Osa-aika Päivä|Vaihteleva Osa-aika Päivä|Tarvittaessa Yö|Tarvittaessa Yöt|Kiertävät Vuorot)\b",
                        DepartmentPattern = @",\s*(Turvallisuuspalvelut|Sydän- ja Verisuoniyksikkö|Liikkuva Ryhmä|Lääketieteellis-Kirurginen Yleiskirurgia|Lääketieteellis-Kirurginen|Laatu & Potilasturvallisuus|Käyttäytymisterveys \(Sairaalahoito\)|Käyttäytymisterveys|Synnytys|Tehohoito Liikkuva|Erikoistunut Sydänyksikkö|Kuntoutus|Apteekki|Kuljetustiimi|Radiologia)\b",
                        EmploymentTypePattern = @",\s*(Vaihteleva Osa-aika|Osa-aika|\d+\.\d+\s+HTV)\b",
                        LocationCodePattern = @"\s*-\s*\([A-Z]{2,5}(-[A-Z]{2,5})*\)",
                        IdentifierPattern = @"-?\d{4,5}\b|\b\d{4}\s+\d+\.\d+\s+HTV\b|\(\d{4,5}\)|\s+\d+\.\d+\s+HTV\b",
                        LocationDetailPattern = @"\s*-\s*[^,]+\s+(Kauppakeskus|Koulutuskeskus|Välitys)|,\s*[^,]+\s+(Kauppakeskus|Koulutuskeskus|Välitys)"
                    }
                },
                // Czech
                {
                    "cs", new JobTitleLanguagePatterns
                    {
                        ShiftPattern = @",\s*(Večerní Směna|Denní Směna|Noční Směna|Částečný Úvazek Den|Variabilní Částečný Úvazek Den|Na Zavolání Noc|Na Zavolání Noci|Rotující Směny)\b",
                        DepartmentPattern = @",\s*(Bezpečnostní Služby|Kardiologická a Cévní Jednotka|Plovoucí Pool|Lékařsko-Chirurgická Obecná Chirurgie|Lékařsko-Chirurgická|Kvalita & Bezpečnost Pacientů|Behaviorální Zdraví \(Hospitalizace\)|Behaviorální Zdraví|Porod|Intenzivní Péče Plovoucí|Specializovaná Kardiologická Jednotka|Rehabilitace|Lékárna|Transportní Tým|Radiologie)\b",
                        EmploymentTypePattern = @",\s*(Variabilní Částečný Úvazek|Částečný Úvazek|\d+\.\d+\s+FTE)\b",
                        LocationCodePattern = @"\s*-\s*\([A-Z]{2,5}(-[A-Z]{2,5})*\)",
                        IdentifierPattern = @"-?\d{4,5}\b|\b\d{4}\s+\d+\.\d+\s+FTE\b|\(\d{4,5}\)|\s+\d+\.\d+\s+FTE\b",
                        LocationDetailPattern = @"\s*-\s*[^,]+\s+(Nákupní Centrum|Školicí Centrum|Makléřství)|,\s*[^,]+\s+(Nákupní Centrum|Školicí Centrum|Makléřství)"
                    }
                },
                // Slovak
                {
                    "sk", new JobTitleLanguagePatterns
                    {
                        ShiftPattern = @",\s*(Večerná Zmena|Denná Zmena|Nočná Zmena|Čiastočný Úväzok Deň|Variabilný Čiastočný Úväzok Deň|Na Zavolanie Noc|Na Zavolanie Noci|Rotujúce Zmeny)\b",
                        DepartmentPattern = @",\s*(Bezpečnostné Služby|Kardiologická a Cievna Jednotka|Plávajúci Pool|Lekársko-Chirurgická Všeobecná Chirurgia|Lekársko-Chirurgická|Kvalita & Bezpečnosť Pacientov|Behaviorálne Zdravie \(Hospitalizácia\)|Behaviorálne Zdravie|Pôrod|Intenzívna Starostlivosť Plávajúca|Špecializovaná Kardiologická Jednotka|Rehabilitácia|Lekáreň|Transportný Tím|Rádiológia)\b",
                        EmploymentTypePattern = @",\s*(Variabilný Čiastočný Úväzok|Čiastočný Úväzok|\d+\.\d+\s+FTE)\b",
                        LocationCodePattern = @"\s*-\s*\([A-Z]{2,5}(-[A-Z]{2,5})*\)",
                        IdentifierPattern = @"-?\d{4,5}\b|\b\d{4}\s+\d+\.\d+\s+FTE\b|\(\d{4,5}\)|\s+\d+\.\d+\s+FTE\b",
                        LocationDetailPattern = @"\s*-\s*[^,]+\s+(Nákupné Centrum|Školiace Centrum|Maklérsvo)|,\s*[^,]+\s+(Nákupné Centrum|Školiace Centrum|Maklérsvo)"
                    }
                },
                // Greek
                {
                    "el", new JobTitleLanguagePatterns
                    {
                        ShiftPattern = @",\s*(Βραδινή Βάρδια|Ημερήσια Βάρδια|Νυχτερινή Βάρδια|Μερική Απασχόληση Ημέρα|Μεταβλητή Μερική Απασχόληση Ημέρα|Κατά Παραγγελία Νύχτα|Κατά Παραγγελία Νύχτες|Εναλλασσόμενες Βάρδιες)\b",
                        DepartmentPattern = @",\s*(Υπηρεσίες Ασφαλείας|Καρδιολογική και Αγγειακή Μονάδα|Κινητό Σύνολο|Ιατρικο-Χειρουργική Γενική Χειρουργική|Ιατρικο-Χειρουργική|Ποιότητα & Ασφάλεια Ασθενών|Συμπεριφορική Υγεία \(Νοσηλεία\)|Συμπεριφορική Υγεία|Τοκετός|Εντατική Θεραπεία Κινητή|Εξειδικευμένη Καρδιολογική Μονάδα|Αποκατάσταση|Φαρμακείο|Ομάδα Μεταφοράς|Ακτινολογία)\b",
                        EmploymentTypePattern = @",\s*(Μεταβλητή Μερική Απασχόληση|Μερική Απασχόληση|\d+\.\d+\s+ΠΑ)\b",
                        LocationCodePattern = @"\s*-\s*\([A-Z]{2,5}(-[A-Z]{2,5})*\)",
                        IdentifierPattern = @"-?\d{4,5}\b|\b\d{4}\s+\d+\.\d+\s+ΠΑ\b|\(\d{4,5}\)|\s+\d+\.\d+\s+ΠΑ\b",
                        LocationDetailPattern = @"\s*-\s*[^,]+\s+(Εμπορικό Κέντρο|Κέντρο Εκπαίδευσης|Μεσιτεία)|,\s*[^,]+\s+(Εμπορικό Κέντρο|Κέντρο Εκπαίδευσης|Μεσιτεία)"
                    }
                },
                // Bulgarian
                {
                    "bg", new JobTitleLanguagePatterns
                    {
                        ShiftPattern = @",\s*(Вечерна Смяна|Дневна Смяна|Нощна Смяна|Непълно Работно Време Ден|Променливо Непълно Работно Време Ден|По Повикване Нощ|По Повикване Нощи|Ротационни Смени)\b",
                        DepartmentPattern = @",\s*(Охранителни Услуги|Кардиологично и Съдово Отделение|Подвижен Пул|Медико-Хирургична Обща Хирургия|Медико-Хирургична|Качество & Безопасност на Пациентите|Поведенческо Здраве \(Болнично\)|Поведенческо Здраве|Раждане|Интензивни Грижи Подвижни|Специализирано Кардиологично Отделение|Рехабилитация|Аптека|Транспортен Екип|Рентгенология)\b",
                        EmploymentTypePattern = @",\s*(Променливо Непълно Работно Време|Непълно Работно Време|\d+\.\d+\s+ПРВ)\b",
                        LocationCodePattern = @"\s*-\s*\([A-Z]{2,5}(-[A-Z]{2,5})*\)",
                        IdentifierPattern = @"-?\d{4,5}\b|\b\d{4}\s+\d+\.\d+\s+ПРВ\b|\(\d{4,5}\)|\s+\d+\.\d+\s+ПРВ\b",
                        LocationDetailPattern = @"\s*-\s*[^,]+\s+(Търговски Център|Учебен Център|Брокерство)|,\s*[^,]+\s+(Търговски Център|Учебен Център|Брокерство)"
                    }
                },
                // Hungarian
                {
                    "hu", new JobTitleLanguagePatterns
                    {
                        ShiftPattern = @",\s*(Esti Műszak|Nappali Műszak|Éjszakai Műszak|Részmunkaidő Nap|Változó Részmunkaidő Nap|Behívás Éjszaka|Behívás Éjszakák|Váltott Műszakok)\b",
                        DepartmentPattern = @",\s*(Biztonsági Szolgálatok|Szív- és Érrendszeri Egység|Lebegő Pool|Orvosi-Sebészeti Általános Sebészet|Orvosi-Sebészeti|Minőség & Betegbiztonság|Viselkedési Egészség \(Kórházi\)|Viselkedési Egészség|Szülés|Intenzív Ellátás Lebegő|Specializált Szívegység|Rehabilitáció|Gyógyszertár|Szállítási Csapat|Radiológia)\b",
                        EmploymentTypePattern = @",\s*(Változó Részmunkaidő|Részmunkaidő|\d+\.\d+\s+FTE)\b",
                        LocationCodePattern = @"\s*-\s*\([A-Z]{2,5}(-[A-Z]{2,5})*\)",
                        IdentifierPattern = @"-?\d{4,5}\b|\b\d{4}\s+\d+\.\d+\s+FTE\b|\(\d{4,5}\)|\s+\d+\.\d+\s+FTE\b",
                        LocationDetailPattern = @"\s*-\s*[^,]+\s+(Bevásárlóközpont|Képzési Központ|Brókerház)|,\s*[^,]+\s+(Bevásárlóközpont|Képzési Központ|Brókerház)"
                    }
                },
                // Croatian
                {
                    "hr", new JobTitleLanguagePatterns
                    {
                        ShiftPattern = @",\s*(Večernja Smjena|Dnevna Smjena|Noćna Smjena|Nepuno Radno Vrijeme Dan|Varijabilno Nepuno Radno Vrijeme Dan|Po Pozivu Noć|Po Pozivu Noći|Rotirajuće Smjene)\b",
                        DepartmentPattern = @",\s*(Sigurnosne Usluge|Kardiološka i Vaskularna Jedinica|Plutajući Pool|Medicinsko-Kirurška Opća Kirurgija|Medicinsko-Kirurška|Kvaliteta & Sigurnost Pacijenata|Bihevioralno Zdravlje \(Bolnička\)|Bihevioralno Zdravlje|Porođaj|Intenzivna Skrb Plutajuća|Specijalizirana Kardiološka Jedinica|Rehabilitacija|Ljekarna|Transportni Tim|Radiologija)\b",
                        EmploymentTypePattern = @",\s*(Varijabilno Nepuno Radno Vrijeme|Nepuno Radno Vrijeme|\d+\.\d+\s+FTE)\b",
                        LocationCodePattern = @"\s*-\s*\([A-Z]{2,5}(-[A-Z]{2,5})*\)",
                        IdentifierPattern = @"-?\d{4,5}\b|\b\d{4}\s+\d+\.\d+\s+FTE\b|\(\d{4,5}\)|\s+\d+\.\d+\s+FTE\b",
                        LocationDetailPattern = @"\s*-\s*[^,]+\s+(Trgovački Centar|Centar za Obuku|Brokerstvo)|,\s*[^,]+\s+(Trgovački Centar|Centar za Obuku|Brokerstvo)"
                    }
                },
                // Estonian
                {
                    "et", new JobTitleLanguagePatterns
                    {
                        ShiftPattern = @",\s*(Õhtuvahetus|Päevavahetus|Öövahetus|Osaaeg Päev|Muutuv Osaaeg Päev|Vajaduse Korral Öö|Vajaduse Korral Ööd|Vahetuvad Vahetused)\b",
                        DepartmentPattern = @",\s*(Turvateenused|Südame- ja Veresoonkonna Üksus|Ujuv Bassein|Meditsiini-Kirurgiline Üldkirurgia|Meditsiini-Kirurgiline|Kvaliteet & Patsiendi Ohutus|Käitumuslik Tervis \(Haiglaravi\)|Käitumuslik Tervis|Sünnitus|Intensiivravi Ujuv|Spetsialiseeritud Südameüksus|Rehabilitatsioon|Apteek|Transpordi Meeskond|Radioloogia)\b",
                        EmploymentTypePattern = @",\s*(Muutuv Osaaeg|Osaaeg|\d+\.\d+\s+TTE)\b",
                        LocationCodePattern = @"\s*-\s*\([A-Z]{2,5}(-[A-Z]{2,5})*\)",
                        IdentifierPattern = @"-?\d{4,5}\b|\b\d{4}\s+\d+\.\d+\s+TTE\b|\(\d{4,5}\)|\s+\d+\.\d+\s+TTE\b",
                        LocationDetailPattern = @"\s*-\s*[^,]+\s+(Kaubanduskeskus|Koolituskeskus|Maakleritöö)|,\s*[^,]+\s+(Kaubanduskeskus|Koolituskeskus|Maakleritöö)"
                    }
                },
                // Latvian
                {
                    "lv", new JobTitleLanguagePatterns
                    {
                        ShiftPattern = @",\s*(Vakara Maiņa|Dienas Maiņa|Nakts Maiņa|Nepilna Slodze Diena|Mainīga Nepilna Slodze Diena|Pēc Nepieciešamības Nakts|Pēc Nepieciešamības Naktis|Rotējošās Maiņas)\b",
                        DepartmentPattern = @",\s*(Drošības Pakalpojumi|Sirds un Asinsvadu Nodaļa|Peldošais Baseins|Medicīniski-Ķirurģiskā Vispārējā Ķirurģija|Medicīniski-Ķirurģiskā|Kvalitāte & Pacienta Drošība|Uzvedības Veselība \(Stacionārs\)|Uzvedības Veselība|Dzemdības|Intensīvā Aprūpe Peldošā|Specializētā Sirds Nodaļa|Rehabilitācija|Aptieka|Transporta Komanda|Radioloģija)\b",
                        EmploymentTypePattern = @",\s*(Mainīga Nepilna Slodze|Nepilna Slodze|\d+\.\d+\s+PSL)\b",
                        LocationCodePattern = @"\s*-\s*\([A-Z]{2,5}(-[A-Z]{2,5})*\)",
                        IdentifierPattern = @"-?\d{4,5}\b|\b\d{4}\s+\d+\.\d+\s+PSL\b|\(\d{4,5}\)|\s+\d+\.\d+\s+PSL\b",
                        LocationDetailPattern = @"\s*-\s*[^,]+\s+(Tirdzniecības Centrs|Apmācību Centrs|Starpniecība)|,\s*[^,]+\s+(Tirdzniecības Centrs|Apmācību Centrs|Starpniecība)"
                    }
                },
                // Lithuanian
                {
                    "lt", new JobTitleLanguagePatterns
                    {
                        ShiftPattern = @",\s*(Vakarinė Pamaina|Dieninė Pamaina|Naktinė Pamaina|Nepilnas Etatas Diena|Kintamas Nepilnas Etatas Diena|Pagal Poreikį Naktis|Pagal Poreikį Naktys|Rotacinės Pamainos)\b",
                        DepartmentPattern = @",\s*(Saugumo Paslaugos|Širdies ir Kraujagyslių Skyrius|Plaukiojantis Baseinas|Medicinos-Chirurgijos Bendroji Chirurgija|Medicinos-Chirurgijos|Kokybė & Paciento Saugumas|Elgesio Sveikata \(Stacionaras\)|Elgesio Sveikata|Gimdymas|Intensyvi Priežiūra Plaukiojanti|Specializuotas Širdies Skyrius|Reabilitacija|Vaistinė|Transporto Komanda|Radiologija)\b",
                        EmploymentTypePattern = @",\s*(Kintamas Nepilnas Etatas|Nepilnas Etatas|\d+\.\d+\s+VTE)\b",
                        LocationCodePattern = @"\s*-\s*\([A-Z]{2,5}(-[A-Z]{2,5})*\)",
                        IdentifierPattern = @"-?\d{4,5}\b|\b\d{4}\s+\d+\.\d+\s+VTE\b|\(\d{4,5}\)|\s+\d+\.\d+\s+VTE\b",
                        LocationDetailPattern = @"\s*-\s*[^,]+\s+(Prekybos Centras|Mokymo Centras|Tarpininkavimas)|,\s*[^,]+\s+(Prekybos Centras|Mokymo Centras|Tarpininkavimas)"
                    }
                },
                // Romanian
                {
                    "ro", new JobTitleLanguagePatterns
                    {
                        ShiftPattern = @",\s*(Schimbul de Seară|Schimbul de Zi|Schimbul de Noapte|Normă Parțială Zi|Normă Parțială Variabilă Zi|La Cerere Noapte|La Cerere Nopți|Schimburi Rotative)\b",
                        DepartmentPattern = @",\s*(Servicii de Securitate|Unitatea Cardiacă și Vasculară|Pool Plutitor|Chirurgie Generală Medicală-Chirurgicală|Medicală-Chirurgicală|Calitate & Siguranța Pacientului|Sănătate Comportamentală \(Spitalizare\)|Sănătate Comportamentală|Naștere|Terapie Intensivă Plutitoare|Unitate Specializată Cardiacă|Reabilitare|Farmacie|Echipa de Transport|Radiologie)\b",
                        EmploymentTypePattern = @",\s*(Normă Parțială Variabilă|Normă Parțială|\d+\.\d+\s+ETC)\b",
                        LocationCodePattern = @"\s*-\s*\([A-Z]{2,5}(-[A-Z]{2,5})*\)",
                        IdentifierPattern = @"-?\d{4,5}\b|\b\d{4}\s+\d+\.\d+\s+ETC\b|\(\d{4,5}\)|\s+\d+\.\d+\s+ETC\b",
                        LocationDetailPattern = @"\s*-\s*[^,]+\s+(Centru Comercial|Centru de Formare|Brokeraj)|,\s*[^,]+\s+(Centru Comercial|Centru de Formare|Brokeraj)"
                    }
                },
                // Slovenian
                {
                    "sl", new JobTitleLanguagePatterns
                    {
                        ShiftPattern = @",\s*(Večerna Izmena|Dnevna Izmena|Nočna Izmena|Skrajšan Delovni Čas Dan|Spremenljiv Skrajšan Delovni Čas Dan|Po Potrebi Noč|Po Potrebi Noči|Rotirajoče Izmene)\b",
                        DepartmentPattern = @",\s*(Varnostne Storitve|Srčno-žilna Enota|Plavajoči Bazen|Medicinsko-kirurška Splošna Kirurgija|Medicinsko-kirurška|Kakovost & Varnost Bolnikov|Vedenjsko Zdravje \(Bolnišnično\)|Vedenjsko Zdravje|Porod|Intenzivna Nega Plavajoča|Specializirana Srčna Enota|Rehabilitacija|Lekarna|Transportna Ekipa|Radiologija)\b",
                        EmploymentTypePattern = @",\s*(Spremenljiv Skrajšan Delovni Čas|Skrajšan Delovni Čas|\d+\.\d+\s+ZDČ)\b",
                        LocationCodePattern = @"\s*-\s*\([A-Z]{2,5}(-[A-Z]{2,5})*\)",
                        IdentifierPattern = @"-?\d{4,5}\b|\b\d{4}\s+\d+\.\d+\s+ZDČ\b|\(\d{4,5}\)|\s+\d+\.\d+\s+ZDČ\b",
                        LocationDetailPattern = @"\s*-\s*[^,]+\s+(Nakupovalni Center|Center za Usposabljanje|Posredništvo)|,\s*[^,]+\s+(Nakupovalni Center|Center za Usposabljanje|Posredništvo)"
                    }
                },
                // Maltese
                {
                    "mt", new JobTitleLanguagePatterns
                    {
                        ShiftPattern = @",\s*(Xogħol ta' Filgħaxija|Xogħol ta' Nhar|Xogħol ta' Lejl|Part-Time Nhar|Part-Time Varjabbli Nhar|Skont il-Ħtieġa Lejl|Skont il-Ħtieġa Lejali|Xogħol Rotatorju)\b",
                        DepartmentPattern = @",\s*(Servizzi ta' Sigurtà|Unità Kardijaka u Vaskulari|Pool li Jgħum|Kirurġija Ġenerali Medika-Kirurġika|Medika-Kirurġika|Kwalità & Sigurtà tal-Pazjent|Saħħa Komportamentali \(Sptar\)|Saħħa Komportamentali|Twelid|Kura Intensiva li Tgħum|Unità Kardijaka Speċjalizzata|Riabilitazzjoni|Spiżerija|Tim tat-Trasport|Radjoloġija)\b",
                        EmploymentTypePattern = @",\s*(Part-Time Varjabbli|Part-Time|\d+\.\d+\s+FTE)\b",
                        LocationCodePattern = @"\s*-\s*\([A-Z]{2,5}(-[A-Z]{2,5})*\)",
                        IdentifierPattern = @"-?\d{4,5}\b|\b\d{4}\s+\d+\.\d+\s+FTE\b|\(\d{4,5}\)|\s+\d+\.\d+\s+FTE\b",
                        LocationDetailPattern = @"\s*-\s*[^,]+\s+(Ċentru Kummerċjali|Ċentru ta' Taħriġ|Sensar)|,\s*[^,]+\s+(Ċentru Kummerċjali|Ċentru ta' Taħriġ|Sensar)"
                    }
                },
                // Hindi
                {
                    "hi", new JobTitleLanguagePatterns
                    {
                        ShiftPattern = @",\s*(शाम की पाली|दिन की पाली|रात की पाली|अंशकालिक दिन|परिवर्तनीय अंशकालिक दिन|आवश्यकतानुसार रात|आवश्यकतानुसार रातें|घूर्णी पालियां)",
                        DepartmentPattern = @",\s*(सुरक्षा सेवाएं|हृदय और संवहनी इकाई|तैरता पूल|चिकित्सा-शल्य चिकित्सा सामान्य शल्य चिकित्सा|चिकित्सा-शल्य चिकित्सा|गुणवत्ता और रोगी सुरक्षा|व्यवहारिक स्वास्थ्य \(अस्पताल में भर्ती\)|व्यवहारिक स्वास्थ्य|प्रसव|गहन देखभाल तैरता|विशेषज्ञ हृदय इकाई|पुनर्वास|फार्मेसी|परिवहन टीम|रेडियोलॉजी)",
                        EmploymentTypePattern = @",\s*(परिवर्तनीय अंशकालिक|अंशकालिक|\d+\.\d+\s+FTE)",
                        LocationCodePattern = @"\s*-\s*\([A-Z]{2,5}(-[A-Z]{2,5})*\)",
                        IdentifierPattern = @"-\d{4,5}|\(\d{4,5}\)",
                        LocationDetailPattern = @"\s*-\s*[^,]+\s+(शॉपिंग सेंटर|प्रशिक्षण केंद्र|दलाली)|,\s*[^,]+\s+(शॉपिंग सेंटर|प्रशिक्षण केंद्र|दलाली)"
                    }
                },
                // Malay
                {
                    "ms", new JobTitleLanguagePatterns
                    {
                        ShiftPattern = @",\s*(Syif Petang|Syif Siang|Syif Malam|Separuh Masa Siang|Separuh Masa Berubah Siang|Atas Panggilan Malam|Atas Panggilan Malam-malam|Syif Bergilir)\b",
                        DepartmentPattern = @",\s*(Perkhidmatan Keselamatan|Unit Jantung dan Vaskular|Kolam Terapung|Pembedahan Am Perubatan-Pembedahan|Perubatan-Pembedahan|Kualiti & Keselamatan Pesakit|Kesihatan Tingkah Laku \(Pesakit Dalam\)|Kesihatan Tingkah Laku|Bersalin|Penjagaan Rapi Terapung|Unit Jantung Khusus|Pemulihan|Farmasi|Pasukan Pengangkutan|Radiologi)\b",
                        EmploymentTypePattern = @",\s*(Separuh Masa Berubah|Separuh Masa|\d+\.\d+\s+FTE)\b",
                        LocationCodePattern = @"\s*-\s*\([A-Z]{2,5}(-[A-Z]{2,5})*\)",
                        IdentifierPattern = @"-?\d{4,5}\b|\b\d{4}\s+\d+\.\d+\s+FTE\b|\(\d{4,5}\)|\s+\d+\.\d+\s+FTE\b",
                        LocationDetailPattern = @"\s*-\s*[^,]+\s+(Pusat Membeli-belah|Pusat Latihan|Pembrokeran)|,\s*[^,]+\s+(Pusat Membeli-belah|Pusat Latihan|Pembrokeran)"
                    }
                },
                // Chinese
                {
                    "zh", new JobTitleLanguagePatterns
                    {
                        ShiftPattern = @",\s*(晚班|日班|夜班|兼职日班|可变兼职日班|随叫随到夜班|随叫随到夜班们|轮班)",
                        DepartmentPattern = @",\s*(安保服务|心血管科|浮动池|内外科普通外科|内外科|质量与患者安全|行为健康\(住院\)|行为健康|产科|重症监护浮动|专科心脏科|康复科|药房|运输团队|放射科)",
                        EmploymentTypePattern = @",\s*(可变兼职|兼职|\d+\.\d+\s+全职当量)",
                        LocationCodePattern = @"\s*-\s*\([A-Z]{2,5}(-[A-Z]{2,5})*\)",
                        IdentifierPattern = @"-\d{4,5}|\(\d{4,5}\)",
                        LocationDetailPattern = @"\s*-\s*[^,]+\s+(购物中心|培训中心|经纪)|,\s*[^,]+\s+(购物中心|培训中心|经纪)"
                    }
                },
                // Tamil
                {
                    "ta", new JobTitleLanguagePatterns
                    {
                        ShiftPattern = @",\s*(மாலை ஷிப்ட்|பகல் ஷிப்ட்|இரவு ஷிப்ட்|பகுதி நேர பகல்|மாறுபடும் பகுதி நேர பகல்|தேவைக்கேற்ப இரவு|தேவைக்கேற்ப இரவுகள்|சுழற்சி ஷிப்ட்கள்)\b",
                        DepartmentPattern = @",\s*(பாதுகாப்பு சேவைகள்|இதய மற்றும் இரத்த நாள பிரிவு|மிதக்கும் குளம்|மருத்துவ-அறுவை சிகிச்சை பொது அறுவை சிகிச்சை|மருத்துவ-அறுவை சிகிச்சை|தரம் & நோயாளி பாதுகாப்பு|நடத்தை சுகாதாரம் \(மருத்துவமனை\)|நடத்தை சுகாதாரம்|பிரசவம்|தீவிர சிகிச்சை மிதக்கும்|சிறப்பு இதய பிரிவு|மறுவாழ்வு|மருந்தகம்|போக்குவரத்து குழு|கதிரியக்கவியல்)\b",
                        EmploymentTypePattern = @",\s*(மாறுபடும் பகுதி நேரம்|பகுதி நேரம்|\d+\.\d+\s+FTE)\b",
                        LocationCodePattern = @"\s*-\s*\([A-Z]{2,5}(-[A-Z]{2,5})*\)",
                        IdentifierPattern = @"-?\d{4,5}\b|\b\d{4}\s+\d+\.\d+\s+FTE\b|\(\d{4,5}\)|\s+\d+\.\d+\s+FTE\b",
                        LocationDetailPattern = @"\s*-\s*[^,]+\s+(வணிக மையம்|பயிற்சி மையம்|தரகு)|,\s*[^,]+\s+(வணிக மையம்|பயிற்சி மையம்|தரகு)"
                    }
                },
                // Japanese
                {
                    "ja", new JobTitleLanguagePatterns
                    {
                        ShiftPattern = @",\s*(夕方シフト|日勤シフト|夜勤シフト|パートタイム日勤|変動パートタイム日勤|オンコール夜勤|オンコール夜勤複数|ローテーションシフト)",
                        DepartmentPattern = @",\s*(セキュリティサービス|心血管ユニット|フロートプール|内科外科一般外科|内科外科|品質・患者安全|行動健康\(入院\)|行動健康|分娩|集中治療フロート|専門心臓ユニット|リハビリテーション|薬局|輸送チーム|放射線科)",
                        EmploymentTypePattern = @",\s*(変動パートタイム|パートタイム|\d+\.\d+\s+常勤換算)",
                        LocationCodePattern = @"\s*-\s*\([A-Z]{2,5}(-[A-Z]{2,5})*\)",
                        IdentifierPattern = @"-\d{4,5}|\(\d{4,5}\)",
                        LocationDetailPattern = @"\s*-\s*[^,]+\s+(ショッピングセンター|トレーニングセンター|仲介)|,\s*[^,]+\s+(ショッピングセンター|トレーニングセンター|仲介)"
                    }
                },
            };
            return jobTitleWordSets;
        }

        /// <summary>
        /// Optimized language detection with caching and static initialization
        /// </summary>
        /// <param name="text">Text to detect language for</param>
        /// <returns>Detected language code or "en" as fallback</returns>
        private static string DetectLanguageOptimized(string text)
        {
            if (string.IsNullOrEmpty(text))
                return "en";

            // Check cache first
            if (_languageCache.TryGetValue(text, out string cachedLanguage))
                return cachedLanguage;

            // Detect language using the static detector
            string detectedLanguage;
            try
            {
                detectedLanguage = _languageDetector.Value.Detect(text) ?? "en";
            }
            catch
            {
                // Fallback to English if detection fails
                detectedLanguage = "en";
            }

            // Cache the result if cache isn't too large
            if (_languageCache.Count < MaxCacheSize)
            {
                _languageCache.TryAdd(text, detectedLanguage);
            }

            return detectedLanguage;
        }

        public static string GetDeepCleanCompanyName(string companyName)
        {
            if (string.IsNullOrEmpty(companyName)) return string.Empty;

            // Use optimized language detection with caching
            string detectedLanguage = DetectLanguageOptimized(companyName);

            var cleanCompanyName = companyName;

            // Step 1: Remove parentheses content and special characters (language-independent)
            cleanCompanyName = Regex.Replace(cleanCompanyName, @"\(.*?\)", "");
            cleanCompanyName = Regex.Replace(cleanCompanyName, @"[""']", "");

            // Step 2: Handle websites (language-independent)
            var matchWebSite = Regex.Match(cleanCompanyName, @"\p{L}+\.(com|co|org|net|edu|gov|mil|biz|info|eu|de|fr|uk|es|it|jp|cn|in|sg|au|nz)", RegexOptions.IgnoreCase);
            if (matchWebSite.Success)
                return matchWebSite.Value;

            // Step 3: Apply language-specific patterns
            if (LanguageWordSets.TryGetValue(detectedLanguage, out var langPatterns))
            {
                // Remove corporate suffixes for detected language first
                cleanCompanyName = Regex.Replace(cleanCompanyName, langPatterns.SuffixPattern, "", RegexOptions.IgnoreCase);

                // Remove conjunctions and possessives for detected language
                var combinedPattern = $@"({langPatterns.ConjunctionPattern}|{langPatterns.PossessivePattern})";
                cleanCompanyName = Regex.Replace(cleanCompanyName, combinedPattern, " ", RegexOptions.IgnoreCase);
            }

            // Step 4: Apply common English patterns as fallback
            if (detectedLanguage != "en" && LanguageWordSets.TryGetValue("en", out var enPatterns))
            {
                cleanCompanyName = Regex.Replace(cleanCompanyName, enPatterns.SuffixPattern, "", RegexOptions.IgnoreCase);
            }

            // Step 5: Remove leftover symbols like . , ; : at the end or between words (language-independent)
            cleanCompanyName = Regex.Replace(cleanCompanyName, "[.,;:]+", "");

            // Step 6: Handle common abbreviations (language-independent)
            cleanCompanyName = Regex.Replace(cleanCompanyName, @"\b(Grp|Gp)\b", "Group", RegexOptions.IgnoreCase);
            cleanCompanyName = Regex.Replace(cleanCompanyName, @"®|™|©", "", RegexOptions.IgnoreCase);

            // Step 7: Clean up whitespace and special characters
            cleanCompanyName = Regex.Replace(cleanCompanyName, @"\s+", " ").Trim();

            // Step 8: Tokenize and singularize (with blacklist)
            cleanCompanyName = ApplyTokenizeAndSingularize(cleanCompanyName, detectedLanguage);

            // Final trim to remove any leading/trailing spaces before returning
            cleanCompanyName = cleanCompanyName.Trim();

            return string.IsNullOrWhiteSpace(cleanCompanyName) ? companyName : cleanCompanyName;
        }

        /// <summary>
        /// Applies tokenization and singularization to the input string based on the detected language.
        /// </summary>
        /// <param name="input"></param>
        /// <param name="detectedLanguage"></param>
        /// <returns></returns>
        private static string ApplyTokenizeAndSingularize(string input, string detectedLanguage)
        {
            // Blacklist: languages for which this step should NOT run
            var blacklist = new HashSet<string> { "hi" }; // Add more language codes as needed
            if (blacklist.Contains(detectedLanguage))
                return input;

            // Tokenize using Unicode letters
            var words = Regex.Matches(input, @"\p{L}+").OfType<Match>().Select(m => m.Value).ToArray();
            // Singularize: if more than 1 word and word length > 6, trim trailing 's'
            var result = string.Join(" ", words.Select(a => words.Length > 1 && a.Length > 6 ? a.TrimEnd('s') : a));
            return result;
        }

        /// <summary>
        /// Cleans job posting titles by removing noise, standardizing formatting, and normalizing job titles
        /// </summary>
        /// <param name="jobTitle">The job title to clean</param>
        /// <returns>Cleaned job title</returns>
        public static string GetCleanJobPostingTitle(string jobTitle)
        {
            if (string.IsNullOrEmpty(jobTitle)) return string.Empty;

            // Use optimized language detection with caching
            string detectedLanguage = DetectLanguageOptimized(jobTitle);

            // Override language detection for specific patterns to ensure correct processing
            if (Regex.IsMatch(jobTitle, @"[\u4e00-\u9fff]")) detectedLanguage = "zh"; // Chinese characters
            else if (Regex.IsMatch(jobTitle, @"[\u3040-\u309f\u30a0-\u30ff]")) detectedLanguage = "ja"; // Japanese characters
            else if (Regex.IsMatch(jobTitle, @"[\u0900-\u097f]")) detectedLanguage = "hi"; // Hindi characters

            var cleanJobTitle = jobTitle;

            // Step 1: Remove location codes in parentheses (e.g., "(LAC)", "(PVSP)")
            if (JobTitleLanguageWordSets.TryGetValue(detectedLanguage, out var jobLangPatterns))
            {
                cleanJobTitle = Regex.Replace(cleanJobTitle, jobLangPatterns.LocationCodePattern, "", RegexOptions.IgnoreCase);
            }

            // Step 2: Remove shift information
            if (jobLangPatterns != null)
            {
                cleanJobTitle = Regex.Replace(cleanJobTitle, jobLangPatterns.ShiftPattern, "", RegexOptions.IgnoreCase);
            }

            // Step 3: Remove department/unit information
            if (jobLangPatterns != null)
            {
                cleanJobTitle = Regex.Replace(cleanJobTitle, jobLangPatterns.DepartmentPattern, "", RegexOptions.IgnoreCase);
            }

            // Step 4: Remove employment type indicators
            if (jobLangPatterns != null)
            {
                cleanJobTitle = Regex.Replace(cleanJobTitle, jobLangPatterns.EmploymentTypePattern, "", RegexOptions.IgnoreCase);
            }

            // Step 5: Remove specific identifiers and numbers
            if (jobLangPatterns != null)
            {
                cleanJobTitle = Regex.Replace(cleanJobTitle, jobLangPatterns.IdentifierPattern, "", RegexOptions.IgnoreCase);
            }

            // Step 6: Remove location details
            if (jobLangPatterns != null)
            {
                cleanJobTitle = Regex.Replace(cleanJobTitle, jobLangPatterns.LocationDetailPattern, "", RegexOptions.IgnoreCase);
            }

            // Step 6.5: Remove remaining department patterns without comma requirement
            cleanJobTitle = Regex.Replace(cleanJobTitle, @",\s*(Day Shift|Night Shift|Evening Shift),\s*(Med Surg|Radiology|Pharmacy|Rehabilitation)\b", "", RegexOptions.IgnoreCase);

            // Step 7: Handle specific patterns that need special treatment
            cleanJobTitle = HandleSpecialJobTitlePatterns(cleanJobTitle);

            // Step 8: Apply job title standardizations
            cleanJobTitle = ApplyJobTitleStandardizations(cleanJobTitle);

            // Step 9: Handle parentheses - preserve certifications but remove other content
            cleanJobTitle = CleanJobTitleParentheses(cleanJobTitle);

            // Step 10: Handle specific RN redundancy
            cleanJobTitle = Regex.Replace(cleanJobTitle, @"Registered Nurse \(RN\),", "Registered Nurse,", RegexOptions.IgnoreCase);

            // Step 11: Clean up punctuation and whitespace
            cleanJobTitle = Regex.Replace(cleanJobTitle, @"[,;:]+", ",");
            cleanJobTitle = Regex.Replace(cleanJobTitle, @"\s*-\s*", " ");
            cleanJobTitle = Regex.Replace(cleanJobTitle, @"\s+", " ").Trim();
            cleanJobTitle = Regex.Replace(cleanJobTitle, @"^,\s*|,\s*$", "").Trim(); // Remove leading/trailing commas

            // Final cleanup
            cleanJobTitle = cleanJobTitle.Trim();

            return string.IsNullOrWhiteSpace(cleanJobTitle) ? jobTitle : cleanJobTitle;
        }

        /// <summary>
        /// Applies job title specific standardizations
        /// </summary>
        /// <param name="jobTitle">The job title to standardize</param>
        /// <returns>Standardized job title</returns>
        private static string ApplyJobTitleStandardizations(string jobTitle)
        {
            // VP standardization
            jobTitle = Regex.Replace(jobTitle, @"\bVP\b", "Vice President", RegexOptions.IgnoreCase);

            // Specific business rule transformations
            jobTitle = Regex.Replace(jobTitle, @"\bFloors\b", "Flooring Specialist", RegexOptions.IgnoreCase);

            // Handle specific patterns that need special treatment
            jobTitle = Regex.Replace(jobTitle, @"^Catering\s*-\s*", "", RegexOptions.IgnoreCase);

            // Handle specific dash patterns that need prefix removal
            if (jobTitle.Contains("Intensive Case Manager") && jobTitle.Contains("-"))
            {
                // Extract just the "Intensive Case Manager" part
                var match = Regex.Match(jobTitle, @"(Intensive Case Manager)", RegexOptions.IgnoreCase);
                if (match.Success)
                {
                    jobTitle = match.Value;
                }
            }

            return jobTitle;
        }

        /// <summary>
        /// Cleans parentheses in job titles, preserving certifications but removing other content
        /// </summary>
        /// <param name="jobTitle">The job title to clean</param>
        /// <returns>Job title with cleaned parentheses</returns>
        private static string CleanJobTitleParentheses(string jobTitle)
        {
            // Preserve important certifications and abbreviations (multi-language)
            var preservePattern = @"\b(RN|CNA|LCSW|LAC|OR|PharmD|MD|PhD|MSN|BSN|LPN|LVN|CRT|RRT|PT|OT|SLP|CRNA|NP|PA|DPT|OTR|COTA|PTA)\b";

            // Find all parentheses content
            var matches = Regex.Matches(jobTitle, @"\(([^)]+)\)");

            foreach (Match match in matches)
            {
                var content = match.Groups[1].Value;

                // If the content contains important certifications, keep it
                if (!Regex.IsMatch(content, preservePattern, RegexOptions.IgnoreCase))
                {
                    // Remove this parentheses
                    jobTitle = jobTitle.Replace(match.Value, "");
                }
            }

            return jobTitle;
        }

        /// <summary>
        /// Handles special job title patterns that need custom processing
        /// </summary>
        /// <param name="jobTitle">The job title to process</param>
        /// <returns>Processed job title</returns>
        private static string HandleSpecialJobTitlePatterns(string jobTitle)
        {
            // Handle titles with dash that need the prefix preserved
            if (jobTitle.Contains(" - "))
            {
                // For titles like "Registered Nurse - Operating Room", preserve both parts
                if (Regex.IsMatch(jobTitle, @"^(Registered Nurse|Nurse|Technician|Therapist|Assistant|Officer|Manager|Coordinator|Specialist|Analyst|Administrator|Director|Supervisor)\s*-"))
                {
                    // Replace dash with comma for these professional titles
                    jobTitle = Regex.Replace(jobTitle, @"\s*-\s*", ", ");
                }
            }

            return jobTitle;
        }

    }
}
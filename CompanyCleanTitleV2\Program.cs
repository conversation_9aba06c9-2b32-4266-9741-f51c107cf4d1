using System;
using System.Collections.Generic;
using System.Linq;

namespace CompanyCleanTitleV2
{
    internal class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("AVERAGE SALARY EXTRACTION TEST");
            Console.WriteLine(new string('=', 50));

            // Test Average Salary Extraction
            TestAverageSalaryExtraction();

            // Test with sample data
            TestAverageSalaryWithSampleData();

            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }

        private static void TestAverageSalaryExtraction()
        {
            Console.WriteLine("\n" + new string('=', 60));
            Console.WriteLine("TESTING AVERAGE SALARY EXTRACTION");
            Console.WriteLine(new string('=', 60));

            var avgSalary = new AvgSalary();

            // Test cases with average salary mentions
            var testCases = new[]
            {
                new JobPosting
                {
                    Title = "Software Developer",
                    Description = "We are looking for a software developer. Salary around $50,000 annually.",
                    IsSalaryCorrect = false
                },
                new JobPosting
                {
                    Title = "Nurse",
                    Description = "About $17 per hour for this position. Great benefits included.",
                    IsSalaryCorrect = false
                },
                new JobPosting
                {
                    Title = "Manager",
                    Description = "Average compensation of $75,000 per year. Excellent opportunity.",
                    IsSalaryCorrect = false
                },
                new JobPosting
                {
                    Title = "Technician",
                    Description = "Typically earns $60K per year. Full-time position available.",
                    IsSalaryCorrect = false
                },
                new JobPosting
                {
                    Title = "Sales Associate",
                    Description = "Starting at approximately $15.50 hourly. Commission opportunities available.",
                    IsSalaryCorrect = false
                },
                new JobPosting
                {
                    Title = "Analyst",
                    Description = "Competitive salary of about $4,500 monthly. Remote work available.",
                    IsSalaryCorrect = false
                },
                new JobPosting
                {
                    Title = "Assistant",
                    Description = "Rate of $23/hour. 12 hours/week (48 hours within a four-week schedule).",
                    IsSalaryCorrect = false
                },
                new JobPosting
                {
                    Title = "Consultant",
                    Description = "No salary information provided. Great company culture.",
                    IsSalaryCorrect = false
                }
            };

            Console.WriteLine("Testing average salary extraction on sample job descriptions:\n");

            int totalTests = testCases.Length;
            int successfulExtractions = 0;

            foreach (var job in testCases)
            {
                Console.WriteLine($"Job Title: {job.Title}");
                Console.WriteLine($"Description: {job.Description}");
                
                var extractedSalary = avgSalary.ExtractAverageSalary(job);
                
                if (!string.IsNullOrEmpty(extractedSalary))
                {
                    successfulExtractions++;
                    Console.WriteLine($"✓ EXTRACTED: {extractedSalary}");
                }
                else
                {
                    Console.WriteLine("✗ No average salary found");
                }
                
                Console.WriteLine(new string('-', 50));
            }

            Console.WriteLine($"\nTEST SUMMARY:");
            Console.WriteLine($"Total test cases: {totalTests}");
            Console.WriteLine($"Successful extractions: {successfulExtractions}");
            Console.WriteLine($"Success rate: {successfulExtractions * 100.0 / totalTests:F1}%");
        }

        private static void TestAverageSalaryWithSampleData()
        {
            Console.WriteLine("\n" + new string('=', 60));
            Console.WriteLine("TESTING WITH REAL JOB POSTING DATA");
            Console.WriteLine(new string('=', 60));

            var avgSalary = new AvgSalary();
            
            // Sample job data with various salary formats
            var sampleJobs = new[]
            {
                new JobPosting
                {
                    Title = "Security Officer, Evening Shift, Security Services",
                    Description = "Shady Grove Medical Center seeks to hire an experienced Security Officer. Pay Range: $18.31 - $24.90. If the salary range is listed as $0 or if the position is Per Diem (with a fixed rate), salary discussions will take place during the screening process.",
                    CompanyName = "Shady Grove Medical Center",
                    IsSalaryCorrect = false
                },
                new JobPosting
                {
                    Title = "Vascular Access Registered Nurse, PT Day Shift, Heart and Vascular Unit",
                    Description = "White Oak Medical Center seeks to hire an experienced Vascular Access Registered Nurse. Pay Range: $37.17 - $55.76. Qualified candidates will possess 5 years of inpatient nursing experience.",
                    CompanyName = "White Oak Medical Center",
                    IsSalaryCorrect = false
                },
                new JobPosting
                {
                    Title = "Certified Nursing Assistant (CNA), Per Diem Night Shift, Float Pool",
                    Description = "Fort Washington Medical Center seeks to hire an experienced Certified Nursing Assistant. This is a per diem position with a rate of $23/hour. 12 hours/week (48 hours within a four-week schedule).",
                    CompanyName = "Fort Washington Medical Center",
                    IsSalaryCorrect = false
                },
                new JobPosting
                {
                    Title = "Software Engineer",
                    Description = "Join our team as a Software Engineer. We offer competitive compensation around $85,000 annually plus excellent benefits package.",
                    CompanyName = "Tech Solutions Inc",
                    IsSalaryCorrect = false
                },
                new JobPosting
                {
                    Title = "Marketing Coordinator",
                    Description = "Marketing Coordinator position available. Starting salary approximately $45K per year with growth opportunities.",
                    CompanyName = "Marketing Pro LLC",
                    IsSalaryCorrect = false
                }
            };

            int processedCount = 0;
            int foundCount = 0;

            Console.WriteLine($"Processing {sampleJobs.Length} sample jobs...\n");

            foreach (var job in sampleJobs)
            {
                processedCount++;
                Console.WriteLine($"Job #{processedCount}: {job.Title}");
                Console.WriteLine($"Company: {job.CompanyName}");
                Console.WriteLine($"Description: {job.Description.Substring(0, Math.Min(120, job.Description.Length))}...");
                
                var extractedSalary = avgSalary.ExtractAverageSalary(job);
                
                if (!string.IsNullOrEmpty(extractedSalary))
                {
                    foundCount++;
                    Console.WriteLine($"✓ FOUND AVERAGE SALARY: {extractedSalary}");
                }
                else
                {
                    Console.WriteLine("✗ No average salary found");
                }
                
                Console.WriteLine(new string('-', 50));
            }

            Console.WriteLine($"\nSUMMARY:");
            Console.WriteLine($"Jobs processed: {processedCount}");
            Console.WriteLine($"Average salaries found: {foundCount}");
            Console.WriteLine($"Success rate: {foundCount * 100.0 / Math.Max(processedCount, 1):F1}%");
        }
    }
}
